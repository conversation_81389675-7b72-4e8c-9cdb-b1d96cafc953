# test_config.py
from dataclasses import dataclass, field
import os
import pathlib
from typing import List, Dict

def _abs(path: str) -> str:
    return str(pathlib.Path(path).expanduser().resolve())

@dataclass(frozen=True)
class TestSettings:
    # keep regions minimal for test speed
    REGION_KEYS: str = "wtn"
    TEST_MODE =1
    CRM_TICKETS_PATH="/ticketing/tickets"
    TEST_CLIENT_ID: int = 13

    # fast timers so tests complete quickly (seconds-scale)
    PER_DEVICE_RESTORE_STABLE_MIN: float = 0.05    # ~3 seconds
    PER_DEVICE_NOTIFY_AFTER_MIN = 0
    FLAP_SUPPRESS_MIN: float = 0.05
    POLL_INTERVAL_SECS = 10

    # cluster (not the focus here)
    CLUSTER_WINDOW_SECS: int = 30
    CLUSTER_THRESHOLD: int = 5
    FIRST_ESCALATION_MIN: int = 10

    # notification preference
    NOTIFY_PREF: str = "sms_first"

    # staff routing (use fake test numbers/emails)
    SUPPORT_SMS: List[str] = field(default_factory=lambda: ["**********"])
    NOC_HD365_SMS: List[str] = field(default_factory=lambda: ["**********"])
    AFTER_HOURS_EMAILS: List[str] = field(default_factory=lambda: ["<EMAIL>"])

    MODERATE_MAIL: str = "<EMAIL>"
    URGENT_MAIL: str = "<EMAIL>"
    CRITICAL_MAIL: str = "<EMAIL>"

    TIMEZONE: str = "America/New_York"

    # paths (use tmp)
    SMS_STORE_PATH: str = os.path.abspath("./test_sms_store.sqlite3")
    TICKET_STORE_PATH: str = os.path.abspath("./test_ticket_store.sqlite3")

    # UISP endpoints/tokens are irrelevant in test mode; kept to satisfy imports
    NSM_BASE_URL: str = "https://invalid-nms.local"
    NSM_TOKEN: str = "TEST"
    CRM_BASE_URL: str = "https://invalid-crm.local"
    CRM_TOKEN: str = "TEST"

    # site/olt map minimal for test
    LOCATIONS: Dict[str, Dict] = field(default_factory=lambda: {
        "wtn": {"site": "TEST-SITE", "olts": ["OLT-A"]}
    })
