# =========================
# UISP (NSM / network side)
# =========================
NSM_BASE_URL=https://esvc.uisp.com/nms/api/v2.1
NSM_TOKEN=your_nsm_token_here

# =========================
# UISP CRM (customer side)
# =========================
CRM_BASE_URL=https://esvc.uisp.com/api/v1.0
CRM_TOKEN=your_crm_token_here

# Live CRM lookups to enrich contacts (True/False)
USE_CRM_FALLBACK=true
CRM_LOOKUPS_PER_POLL=50

# ==========
# TEST MODE ON OR OFF
# ==========
TEST_MODE=1
SUPPORT_SMS=+1234567890

# ==========
# Polling
# ==========
REGION_KEYS=wtn,ccp,wts,off,wl
POLL_INTERVAL_SECS=30

# =========================
# Per-device thresholds
# =========================
# Wait X minutes before notifying a single-customer outage
PER_DEVICE_NOTIFY_AFTER_MIN=5
# Require X minutes stable before sending "restored"
PER_DEVICE_RESTORE_STABLE_MIN=3
# Suppress noisy flaps within X minutes
FLAP_SUPPRESS_MIN=10

# =========================
# Cluster / escalation rules
# =========================
CLUSTER_WINDOW_SECS=120
CLUSTER_THRESHOLD_N=5
CLUSTER_URGENT_M=10
FIRST_ESCALATION_MIN=10
CLUSTER_RESTORE_STABLE_MIN=3
SITE_CRITICAL_MINUTES=20

# =========================
# Notification preferences
# =========================
NOTIFY_PREF=sms_first   # or: email_first
TIMEZONE=America/New_York
OFFICE_HOURS_START=08:00
OFFICE_HOURS_END=18:00

# =========================
# Staff notifications
# =========================
# Single-address lists for staff mail
MODERATE_MAIL=<EMAIL>
URGENT_MAIL=<EMAIL>
CRITICAL_MAIL=<EMAIL>

# Comma-separated lists (no spaces) for staff SMS
NOC_HD365_SMS=+1234567890
FIELD_SUPERVISOR_SMS=+1234567890,+0987654321

# After-hours escalation email list (comma-separated ok)
AFTER_HOURS_EMAILS=<EMAIL>

# =========================
# SMS (Vitelity)
# =========================
VITELITY_USERNAME=your_vitelity_username
VITELITY_PASSWORD=your_vitelity_password
VITELITY_FROM_NUMBER=1234567890

# =========================
# Storage paths (Linux examples for Ubuntu deployment)
# =========================
# Make sure these directories exist
SMS_STORE_PATH=/var/lib/gandalf-wtn/sms_store.sqlite3
TICKET_STORE_PATH=/var/lib/gandalf-wtn/ticket_store.sqlite3

# =========================
# Ticketing (CRM)
# =========================
# User ID to attribute the initial ticket comment to
CRM_TICKET_USER_ID=1

# =========================
# Email SMTP
# =========================
SMTP_HOST=smtp.office365.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
FROM_EMAIL=<EMAIL>
EMAIL_USE_TLS=True

# =========================
# Celery (optional)
# =========================
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/1
ENABLE_BEAT=1

# =========================
# Safety switches
# =========================
# TEST_MODE=1 => never hit SMS/SMTP; log only
TEST_MODE=1
# DRY_RUN=1 => skip ticket creates/updates and real sends; log actions
DRY_RUN=0
