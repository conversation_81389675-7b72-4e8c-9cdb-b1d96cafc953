#!/usr/bin/env python3
from __future__ import annotations

import argparse
from datetime import datetime, UTC
from typing import Optional

from config import Settings
from uisp_client import UispClient
from notify import send_sms_raw, send_email


def _build_client(settings: Settings) -> UispClient:
    return UispClient(
        base_url=settings.NSM_BASE_URL,   # NSM controller API (e.g., .../nms/api/v2.1)
        token=settings.NSM_TOKEN,
        crm_base=settings.CRM_BASE_URL,   # CRM API (e.g., .../api/v1.0)
        crm_token=settings.CRM_TOKEN,
        timeout=15.0,
    )


def test_mapping(client: UispClient, device_id: str) -> Optional[dict]:
    """
    NSM device -> NSM site -> UCRM linkage -> CRM client + address.
    Returns: {device_id, site_id, crm_client_id, name, address}
    """
    dev = client.nsm_get_device(device_id) or {}
    site = (dev.get("identification") or {}).get("site") or {}
    site_id = site.get("id")
    if not site_id:
        raise RuntimeError("device.identification.site.id missing")

    sdetail = client.nsm_get_site(site_id) or {}
    ucrm = sdetail.get("ucrm") or {}
    ucrm_client = (ucrm.get("client") or {})
    crm_id = ucrm_client.get("id")
    name = ucrm_client.get("name") or site.get("name") or "(unknown)"

    if not crm_id:
        raise RuntimeError("site.ucrm.client.id missing (device not linked to CRM client?)")

    c = client.crm_get_client(int(crm_id)) or {}
    street = (c.get("street1") or "").strip()
    city = (c.get("city") or "").strip()
    zipc = (c.get("zipCode") or "").strip()
    address = ", ".join([x for x in (street, city, zipc) if x])

    return {
        "device_id": device_id,
        "site_id": site_id,
        "crm_client_id": int(crm_id),
        "name": name,
        "address": address or "-",
    }


def test_ticket_create_close(client: UispClient, client_id: int) -> bool:
    print("\n=== SMOKE: CRM ticket create + close ===")
    try:
        now = datetime.now(UTC).strftime("%Y-%m-%d %H:%M:%S UTC")

        payload = {
            "subject": f"ESVC smoke test @ {now}",
            "clientId": int(client_id),
            "status": 0,
            "public": False,
            "activity": [
                {
                    "userId": None,           # comment as system/API
                    "public": False,
                    "comment": {
                        "body": f"Automated smoke test ticket opened at {now}"
                    }
                }
            ]
        }
        created = client.crm_create_ticket(payload) or {}
        tid = created.get("id")
        if not tid:
            raise RuntimeError(f"no ticket id in response: {created!r}")
        print(f"[OK] ticket created id={tid}")

        upd = client.crm_update_ticket(tid, {
            "status": 2,  # Closed
            "activity": [
                {
                    "userId": None,
                    "public": False,
                    "comment": {
                        "body": f"Automated smoke test ticket closed at {now}"
                    }
                }
            ]
        }) or {}
        _ = upd.get("id")  # ensure no error
        print(f"[OK] ticket closed id={tid}")
        return True
    except Exception as e:
        print(f"[ERR] ticket create/close failed: {e}")
        return False


def test_sms(to_number: str, name: str, address: str, device_id: str) -> bool:
    print("\n=== SMOKE: SMS send ===")
    if not to_number:
        print("[SKIP] no SMS recipient provided")
        return False
    try:
        msg = f"[ESVC] Smoke: outage detected for {name or '-'} at {address or '-'} (device {device_id})."
        # send_sms_raw logs provider status internally; here we just call it
        send_sms_raw(to_number, msg)
        print(f"[OK] SMS attempted to {to_number}")
        return True
    except Exception as e:
        print(f"[ERR] SMS failed: {e}")
        return False


def test_email(to_addr: str, name: str, address: str, device_id: str) -> bool:
    print("\n=== SMOKE: Email send ===")
    if not to_addr:
        print("[SKIP] no email recipient provided")
        return False
    try:
        subject = "ESVC smoke test"
        body = (
            "This is a smoke-test email.\n\n"
            f"Device: {device_id}\n"
            f"Client: {name or '-'}\n"
            f"Address: {address or '-'}\n"
            f"Time: {datetime.now(UTC).strftime('%Y-%m-%d %H:%M:%S UTC')}\n"
        )
        send_email([to_addr], subject, body)
        print(f"[OK] email attempted to {to_addr}")
        return True
    except Exception as e:
        print(f"[ERR] email failed: {e}")
        return False


def main():
    ap = argparse.ArgumentParser(description="ESVC Smoke Test (NSM/CRM + SMS/Email/Tickets)")
    ap.add_argument("device_id", nargs="?", help="NSM device GUID to map (optional if --ticket-only)")
    ap.add_argument("--ticket-client", type=int, help="CRM clientId for ticket test (uses mapping if omitted)")
    ap.add_argument("--ticket-only", type=int, metavar="CLIENT_ID", help="Only run ticket test for this clientId")
    ap.add_argument("--sms-to", help="E.164 phone number for SMS test (default: first NOC_HD365_SMS)")
    ap.add_argument("--email-to", help="Email address for email test (default: MODERATE_MAIL/URGENT_MAIL/CRITICAL_MAIL)")
    args = ap.parse_args()

    settings = Settings()
    client = _build_client(settings)

    # If only ticket test requested, do that and exit
    if args.ticket_only:
        test_ticket_create_close(client, args.ticket_only)
        return

    # 1) Mapping (optional but recommended to enrich SMS/email text)
    mapped = None
    if args.device_id:
        mapped = test_mapping(client, args.device_id)
    else:
        print("\n=== SMOKE: NSM → CRM mapping ===")
        print("[SKIP] no device id provided")

    # 2) Ticket create/close (if we know a client id)
    t_client = args.ticket_client
    if t_client is None and mapped and mapped.get("crm_client_id"):
        t_client = int(mapped["crm_client_id"])
    if t_client:
        test_ticket_create_close(client, t_client)
    else:
        print("\n=== SMOKE: CRM ticket create + close ===")
        print("[SKIP] no client id available (pass --ticket-client or provide a mappable device)")

    # Determine text/email recipients (safe defaults to support/NOC)
    sms_to = args.sms_to
    if not sms_to:
        sms_list = getattr(settings, "NOC_HD365_SMS", []) or []
        sms_to = sms_list[0] if sms_list else None

    email_to = args.email_to
    if not email_to:
        email_to = (
            getattr(settings, "MODERATE_MAIL", "") or
            getattr(settings, "URGENT_MAIL", "") or
            getattr(settings, "CRITICAL_MAIL", "")
        )

    name = (mapped or {}).get("name") or "-"
    address = (mapped or {}).get("address") or "-"
    dev = args.device_id or "-"

    # 3) SMS
    test_sms(sms_to, name, address, dev)

    # 4) Email
    test_email(email_to, name, address, dev)


if __name__ == "__main__":
    main()
