# celery_app.py
import os
from celery import Celery

def _getint(name: str, default: int) -> int:
    try:
        return int(os.getenv(name, str(default)))
    except Exception:
        return default

# Celery app (optional; standalone runner works without Celery)
app = Celery(
    "gandalf_wtn",
    broker=os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0"),
    backend=os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/0"),
    include=["tasks"],  # make sure tasks.py is discoverable
)

# General config
app.conf.update(
    timezone=os.getenv("TIMEZONE", "America/New_York"),
    task_routes={"tasks.poll_and_notify": {"queue": "wtn"}},
)

# Optional: periodic polling via Celery Beat (set ENABLE_BEAT=0 to disable)
if os.getenv("ENABLE_BEAT", "1") != "0":
    app.conf.beat_schedule = {
        "wtn-poll": {
            "task": "tasks.poll_and_notify",
            "schedule": _getint("POLL_INTERVAL_SECS", 30),
        }
    }
