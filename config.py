# config.py
from __future__ import annotations
import os
import pathlib
from dataclasses import dataclass, field

try:
    # Prefer the real library if available
    from dotenv import load_dotenv  # type: ignore
except Exception:
    # Minimal fallback loader to avoid hard dependency on python-dotenv
    def load_dotenv(dotenv_path: str | None = None) -> None:
        """
        Load simple KEY=VALUE pairs from a .env file into os.environ if not already set.
        - Supports comments starting with '#'
        - Preserves existing environment variables (does not override)
        - Handles quoted values and basic escaping of \n and \t
        """
        def _parse_line(line: str) -> tuple[str, str] | None:
            line = line.strip()
            if not line or line.startswith("#"):
                return None
            if "=" not in line:
                return None
            k, v = line.split("=", 1)
            k = k.strip()
            v = v.strip().strip("'").strip('"')
            v = v.replace("\\n", "\n").replace("\\t", "\t")
            return (k, v)

        # Resolve default .env path: sibling to this config.py or project root
        if dotenv_path is None:
            here = pathlib.Path(__file__).resolve().parent
            candidates = [here / ".env", here.parent / ".env"]
            for cand in candidates:
                if cand.exists():
                    dotenv_path = str(cand)
                    break
        if not dotenv_path:
            return
        try:
            with open(dotenv_path, "r", encoding="utf-8") as fh:
                for raw in fh:
                    parsed = _parse_line(raw)
                    if not parsed:
                        continue
                    k, v = parsed
                    if k not in os.environ:
                        os.environ[k] = v
        except FileNotFoundError:
            # Silent if .env is missing
            pass
        except Exception:
            # Fail-soft: don't block app startup on .env parse issues
            pass

load_dotenv()

def _env_list(name: str) -> list[str]:
    val = os.getenv(name, "")
    return [s.strip() for s in val.split(",") if s.strip()]

def _abs(path: str) -> str:
    return str(pathlib.Path(path).expanduser().resolve())

@dataclass(frozen=True)
class Settings:
    # NSM (UISP NMS) API
    UISP_BASE_URL: str = os.getenv("UISP_BASE_URL", "https://esvc.uisp.com").rstrip("/")
    NSM_BASE_URL: str = os.getenv("NSM_BASE_URL", "https://esvc.uisp.com/nms/api/v2.1").rstrip("/")
    NSM_TOKEN: str = os.getenv("NSM_TOKEN")

    # CRM API (separate base/token if different app)
    CRM_BASE_URL: str = os.getenv("CRM_BASE_URL", "https://esvc.uisp.com/api/v1.0").rstrip("/")
    CRM_TOKEN: str = os.getenv("CRM_TOKEN")

    USE_CRM_FALLBACK: bool = os.getenv("USE_CRM_FALLBACK", "true").lower() == "true"
    CRM_LOOKUPS_PER_POLL: int = int(os.getenv("CRM_LOOKUPS_PER_POLL", "50"))

    # --- Test / Sandbox mode ---
    TEST_MODE: bool = os.getenv("TEST_MODE", "true").lower() == "true"
    TEST_SMS_TO: list[str] = field(
        default_factory=lambda: _env_list("TEST_SMS_TO")   or ["+17576958080"])  # comma-separated e.g. "+15551234567,+15557654321"
    TEST_EMAIL_TO: str = os.getenv("TEST_EMAIL_TO", "<EMAIL>")  # single mailbox, e.g. "<EMAIL>"
    DRY_RUN_NOTIFICATIONS: bool = os.getenv("DRY_RUN_NOTIFICATIONS", "false").lower() == "true"

    # ---- Regions & polling ----
    REGION_KEYS: str = os.getenv("REGION_KEYS", "wtn,ccp,wts,off,wl")
    POLL_INTERVAL_SECS: int = int(os.getenv("POLL_INTERVAL_SECS", "30"))

    # ---- Per-device timers ----
    PER_DEVICE_THRESHOLD_MIN: int = int(os.getenv("PER_DEVICE_NOTIFY_AFTER_MIN", "3"))
    PER_DEVICE_RESTORE_STABLE_MIN: int = int(os.getenv("PER_DEVICE_RESTORE_STABLE_MIN", "3"))
    FLAP_SUPPRESS_MIN: int = int(os.getenv("FLAP_SUPPRESS_MIN", "10"))

    # ---- Cluster rules ----
    CLUSTER_WINDOW_SECS: int = int(os.getenv("CLUSTER_WINDOW_SECS", "120"))
    CLUSTER_THRESHOLD: int = int(os.getenv("CLUSTER_THRESHOLD_N", "5"))
    CLUSTER_URGENT_M: int = int(os.getenv("CLUSTER_URGENT_M", "10"))
    FIRST_ESCALATION_MIN: int = int(os.getenv("FIRST_ESCALATION_MIN", "10"))
    CLUSTER_RESTORE_STABLE_MIN: int = int(os.getenv("CLUSTER_RESTORE_STABLE_MIN", "3"))
    SITE_CRITICAL_MINUTES: int = int(os.getenv("SITE_CRITICAL_MINUTES", "20"))

    # ---- Notification policy ----
    NOTIFY_PREF: str = os.getenv("NOTIFY_PREF", "sms_first").strip().lower()
    TIMEZONE: str = os.getenv("TIMEZONE", "America/New_York")
    OFFICE_HOURS: dict = field(default_factory=lambda: {
        "start": os.getenv("OFFICE_HOURS_START", "08:00"),
        "end": os.getenv("OFFICE_HOURS_END", "18:00"),
    })

    # ---- Ticketing ----
    # config.py — CRM ticketing settings
    CRM_TICKETS_PATH: str = os.getenv("CRM_TICKETS_PATH", "/ticketing/tickets")
    TICKETS_ASSIGNED_GROUP_ID: int | None = int(os.getenv("TICKETS_ASSIGNED_GROUP_ID", "0") or "0") or None
    TICKETS_ASSIGNED_USER_ID: int | None = int(os.getenv("TICKETS_ASSIGNED_USER_ID", "0") or "0") or None
    TICKETS_CLOSED_STATUS: int = int(
        os.getenv("TICKETS_CLOSED_STATUS", "3"))  # adjust if your CRM uses a different code

    # ---- Mailing lists / staff SMS ----
    SUPPORT_EMAILS: list[str] = field(default_factory=lambda: _env_list("SUPPORT_EMAILS"))
    MODERATE_MAIL: str = os.getenv("MODERATE_MAIL", "<EMAIL>")
    URGENT_MAIL: str = os.getenv("URGENT_MAIL", "<EMAIL>")
    CRITICAL_MAIL: str = os.getenv("CRITICAL_MAIL", "<EMAIL>")
    SUPPORT_SMS: list[str] = field(default_factory=lambda: _env_list("SUPPORT_SMS") or ["+17576958080"])

    NOC_HD365_SMS: list[str] = field(default_factory=lambda: _env_list("NOC_HD365_SMS"))
    FIELD_SUPERVISOR_SMS: list[str] = field(default_factory=lambda: _env_list("FIELD_SUPERVISOR_SMS"))
    AFTER_HOURS_EMAILS: list[str] = field(default_factory=lambda: _env_list("AFTER_HOURS_EMAILS"))

    # ---- Storage ----
    SMS_STORE_PATH: str = os.getenv(
        "SMS_STORE_PATH",
        r"c:\temp\sms_store.sqlite3" if os.name == "nt" else "/var/lib/gandalf-wtn/sms_store.sqlite3"
    )
    TICKET_STORE_PATH: str = os.getenv("TICKET_STORE_PATH", _abs("./ticket_store.sqlite3"))

    # ---- Vitelity SMS ----
    VITELITY_USERNAME: str = os.getenv("VITELITY_USERNAME", "esho_api")
    VITELITY_FROM_NUMBER: str = os.getenv("VITELITY_FROM_NUMBER", "2525486200")
    VITELITY_SEND_API_URL = "https://smsout-api.vitelity.net/api.php"
    VITELITY_API_URL = "https://api.vitelity.net/api.php"
    VITELITY_PASSWORD: str= os.getenv("VITELITY_PASSWORD")

    SMS_RATE_PER_MIN: int = int(os.getenv("SMS_RATE_PER_MIN", "50"))

    # ---- Site/OLT mapping ----
    LOCATIONS: dict[str, dict] = field(default_factory=lambda: {
        "wtn": {
            "site": "b7cda07e-decc-40b0-b7c6-6897135a5645",
            "olts": ["0a0b800d-81d2-4eb1-95ab-8fcf00dec7b6", "faac8f84-fc00-4e36-b06a-59abcb215357"],
        },
        "ccp": {
            "site": "1a782c6d-657f-4a5b-8a46-c3260a114281",
            "olts": ["39e60cec-736d-48f9-b9c5-e4929b43c92a", "3fbfcb3e-97bc-4218-93b6-3c7be73b61a8"],
        },
        "wts": {
            "site": "6725279f-051f-4977-8a74-ac0f3322c21b",
            "olts": ["fa1d0454-14a9-4c5d-aaa0-426671107b0c"],
        },
        "off": {
            "site": "79740732-3b6b-48d3-8a18-7559da9bef4e",
            "olts": ["83f4ffb6-a371-4410-b912-189127c07e4b"],
        },
        "wl": {
            "site": "6d1520a9-1da0-4cdf-b903-9a9942bb9d43",
            "olts": ["64c41c50-e947-4d66-84fa-cbd88594744c"],
        },
    })


settings = Settings()