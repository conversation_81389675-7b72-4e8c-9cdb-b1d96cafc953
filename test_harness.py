#!/usr/bin/env python3
from __future__ import annotations
import os, sys, time, random, argparse, pathlib, logging

# ----------------------------
# Test env & paths (set BEFORE imports so config picks them up)
# ----------------------------
ROOT = pathlib.Path(__file__).resolve().parent
ARTI = ROOT / ".test_artifacts"
ARTI.mkdir(exist_ok=True)

os.environ.setdefault("SMS_STORE_PATH", str(ARTI / "sms_store.sqlite3"))
os.environ.setdefault("TICKET_STORE_PATH", str(ARTI / "ticket_store.sqlite3"))
# Safe mode for outbound sends; set TEST_MODE=0 to actually send
os.environ.setdefault("TEST_MODE", "1")

# Faster cadence + immediate device notify for tests (override as you like)
os.environ.setdefault("POLL_INTERVAL_SECS", "10")
os.environ.setdefault("PER_DEVICE_NOTIFY_AFTER_MIN", "0")
os.environ.setdefault("PER_DEVICE_RESTORE_STABLE_MIN", "1")

import detector as detector_mod          # must import before tasks so we can patch both
import tasks as tasks_mod
from tasks import poll_and_notify as poll_task
from config import settings
from uisp_client import UispClient

log = logging.getLogger("harness")
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(name)s: %(message)s")

# ----------------------------
# Helpers using your existing client
# ----------------------------
def build_real_client() -> UispClient:
    return UispClient(
        base_url=settings.NSM_BASE_URL,
        token=settings.NSM_TOKEN,
        crm_base=settings.CRM_BASE_URL,
        crm_token=settings.CRM_TOKEN,
        timeout=15.0,
    )

def list_region_endpoints(client: UispClient, region_key: str) -> list[dict]:
    """
    Pull the site via client.get_site(site_id) and return its endpoints array.
    """
    loc = settings.LOCATIONS.get(region_key)
    if not loc:
        log.warning("Unknown region '%s' in settings.LOCATIONS; skipping.", region_key)
        return []
    site_id = loc["site"]
    try:
        site = client.nsm_get_site(site_id) or {}
    except Exception as e:
        log.error("Failed to get site %s for region %s: %s", site_id, region_key, e)
        return []
    desc = site.get("description") or {}
    eps = desc.get("endpoints") or []
    return [ep for ep in eps if (ep or {}).get("type") == "endpoint"]

def collect_candidates_by_region(client: UispClient, region_keys: list[str], limit_per_region: int = 50) -> dict[str, list[dict]]:
    """
    Return {region_key: [endpoint, ...]} so we can sample per-region and
    ensure detect_offline_rows(region) returns only that region’s rows.
    """
    out: dict[str, list[dict]] = {}
    for rk in region_keys:
        eps = list_region_endpoints(client, rk)
        random.shuffle(eps)
        out[rk] = eps[:limit_per_region]
    return out

def make_rows_for_region(endpoints: list[dict], k: int) -> list[dict]:
    """
    Convert endpoints from ONE region into synthetic offline rows for that region.
    Only fields needed downstream are included; enrichment happens in your app.
    """
    rows: list[dict] = []
    if not endpoints:
        return rows
    pick = endpoints if len(endpoints) <= k else random.sample(endpoints, k)
    for ep in pick:
        ep_name = (ep.get("name") or "").strip()
        ep_site_id = ep.get("id")
        fake_onu = f"sim-{ep_site_id}"
        rows.append({
            "endpoint": ep_name,
            "endpoint_site_id": ep_site_id,
            "onu_id": fake_onu,
            "client_id": None,
            "address": "-",
        })
    return rows

def scenario_count(base_count: int, scenario: str) -> int:
    if scenario == "minor":
        return max(1, min(base_count, 3))
    if scenario == "moderate":
        return max(2, base_count)
    if scenario == "urgent":
        return max(5, base_count)
    if scenario == "critical":
        return max(10, base_count)
    return max(1, base_count)

# ----------------------------
# Patch both detector & tasks
# ----------------------------
class PatchDetect:
    """
    Patches BOTH:
      detector.detect_offline_rows(region_key)
      tasks.detect_offline_rows(region_key)
    and restores originals on exit.
    """
    def __init__(self, rows_by_region_cycle_fn):
        # callable: (region_key) -> list[dict] for current cycle
        self.rows_by_region_cycle_fn = rows_by_region_cycle_fn
        self._orig_det = None
        self._orig_tasks = None

    def __enter__(self):
        self._orig_det = detector_mod.detect_offline_rows
        self._orig_tasks = getattr(tasks_mod, "detect_offline_rows", None)

        def _fake_detect_offline_rows(region_key: str) -> list[dict]:
            return list(self.rows_by_region_cycle_fn(region_key))

        detector_mod.detect_offline_rows = _fake_detect_offline_rows
        if self._orig_tasks is not None:
            tasks_mod.detect_offline_rows = _fake_detect_offline_rows
        return self

    def __exit__(self, exc_type, exc, tb):
        if self._orig_det is not None:
            detector_mod.detect_offline_rows = self._orig_det
        if self._orig_tasks is not None:
            tasks_mod.detect_offline_rows = self._orig_tasks
        return False

# ----------------------------
# Main
# ----------------------------
def main():
    ap = argparse.ArgumentParser(description="Simulation harness using existing app code.")
    ap.add_argument("scenario", choices=["minor", "moderate", "urgent", "critical"], nargs="?", default="minor")
    ap.add_argument("--regions",
                    default=settings.REGION_KEYS if isinstance(settings.REGION_KEYS, str) else "wtn,ccp,wts,off,wl",
                    help="Comma-separated region keys (default from settings).")
    ap.add_argument("--count", type=int, default=1, help="Endpoints to simulate per region.")
    ap.add_argument("--cycles", type=int, default=3, help="Poll cycles under simulation.")
    ap.add_argument("--interval", type=int, default=int(os.getenv("POLL_INTERVAL_SECS", "30")),
                    help="Seconds between polls.")
    ap.add_argument("--baseline", action="store_true", help="Run one real poll before simulation.")
    ap.add_argument("--restore-on-last", action="store_true",
                    help="Return [] on last cycle to simulate restoration.")
    args = ap.parse_args()

    regions = [s.strip() for s in args.regions.split(",") if s.strip()]
    client = build_real_client()

    if args.baseline:
        log.info("Baseline (real detector) starting...")
        try:
            poll_task.run()
        except Exception as e:
            log.error("Baseline poll failed: %s", e)
        log.info("Baseline complete.\n")

    # Build candidates by region from real UISP, then freeze per-region synthetic rows
    candidates = collect_candidates_by_region(client, regions)
    per_region_k = scenario_count(args.count, args.scenario)
    base_rows_by_region: dict[str, list[dict]] = {
        rk: make_rows_for_region(candidates.get(rk, []), per_region_k) for rk in regions
    }
    total = sum(len(v) for v in base_rows_by_region.values())
    log.info("Scenario '%s': generated %d synthetic offline row(s).", args.scenario, total)

    current_cycle = {"i": 0}

    def rows_for_cycle(region_key: str) -> list[dict]:
        i = current_cycle["i"]
        if args.restore_on_last and (i >= args.cycles - 1):
            return []  # drive your restoration path
        return base_rows_by_region.get(region_key, [])

    with PatchDetect(rows_for_cycle):
        for i in range(args.cycles):
            current_cycle["i"] = i
            log.info("Simulation cycle %d/%d", i + 1, args.cycles)
            try:
                poll_task.run()
            except Exception as e:
                log.error("Poll failed: %s", e)
            if i < args.cycles - 1:
                time.sleep(max(1, args.interval))

    log.info("Simulation complete. Test artifacts in %s", ARTI)

if __name__ == "__main__":
    main()
