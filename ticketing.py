# ticketing.py
from __future__ import annotations

import logging
import sqlite3
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

import requests

log = logging.getLogger("ticketing")


class TicketStore:
    """
    Minimal local sqlite store to keep idempotency and CRM ids.
    Schema is stable and safe to recreate.
    """

    def __init__(self, path: str):
        self.path = Path(path)
        if self.path.parent and not self.path.parent.exists():
            self.path.parent.mkdir(parents=True, exist_ok=True)
        log.info("TicketStore at %s", self.path)
        self._init_db()

    def _connect(self):
        return sqlite3.connect(str(self.path))

    def _init_db(self) -> None:
        # Create table if missing
        with self._connect() as db:
            db.execute("""
                       CREATE TABLE IF NOT EXISTS tickets
                       (
                           id             INTEGER PRIMARY KEY AUTOINCREMENT,
                           incident_key   TEXT UNIQUE,
                           client_id      TEXT,
                           endpoint_name  TEXT,
                           onu_id         TEXT,
                           address        TEXT,
                           status         TEXT,    -- open|closed
                           started_ts     INTEGER, -- epoch seconds
                           closed_ts      INTEGER, -- epoch seconds or NULL
                           last_update_ts INTEGER, -- epoch seconds or NULL
                           crm_ticket_id  TEXT,
                           created_at     INTEGER  -- epoch seconds
                       )
                       """)
            db.commit()
        # Migrate older DBs in place
        self._ensure_columns([
            ("incident_key", "TEXT"),
            ("client_id", "TEXT"),
            ("endpoint_name", "TEXT"),
            ("onu_id", "TEXT"),
            ("address", "TEXT"),
            ("status", "TEXT"),
            ("started_ts", "INTEGER"),
            ("closed_ts", "INTEGER"),
            ("last_update_ts", "INTEGER"),
            ("crm_ticket_id", "TEXT"),
            ("created_at", "INTEGER"),
        ])

    def _ensure_columns(self, cols: list[tuple[str, str]]) -> None:
        with self._connect() as db:
            cur = db.execute("PRAGMA table_info('tickets')")
            existing = {row[1] for row in cur.fetchall()}  # column names
            for name, typ in cols:
                if name not in existing:
                    db.execute(f"ALTER TABLE tickets ADD COLUMN {name} {typ}")
            db.commit()

    # --- helpers ---
    def get(self, incident_key: str) -> Optional[dict]:
        with self._connect() as db:
            cur = db.execute(
                "SELECT incident_key, client_id, endpoint_name, onu_id, address, started_ts, crm_ticket_id, status, last_update_ts "
                "FROM tickets WHERE incident_key=?",
                (incident_key,),
            )
            row = cur.fetchone()
            if not row:
                return None
            keys = [
                "incident_key",
                "client_id",
                "endpoint_name",
                "onu_id",
                "address",
                "started_ts",
                "crm_ticket_id",
                "status",
                "last_update_ts",
            ]
            return dict(zip(keys, row))

    def upsert_open(
        self,
        incident_key: str,
        *,
        client_id: str,
        endpoint_name: str,
        onu_id: str,
        started_ts: int,
        address: str,
    ) -> None:
        ts = int(time.time())
        with self._connect() as db:
            existing = db.execute(
                "SELECT incident_key FROM tickets WHERE incident_key=?", (incident_key,)
            ).fetchone()
            if existing:
                db.execute(
                    "UPDATE tickets SET client_id=?, endpoint_name=?, onu_id=?, address=?, started_ts=?, status=?, last_update_ts=? WHERE incident_key=?",
                    (client_id, endpoint_name, onu_id, address, started_ts, "open", ts, incident_key),
                )
            else:
                db.execute(
                    "INSERT INTO tickets (incident_key, client_id, endpoint_name, onu_id, address, started_ts, crm_ticket_id, status, last_update_ts) "
                    "VALUES (?,?,?,?,?,?,NULL,?,?)",
                    (incident_key, client_id, endpoint_name, onu_id, address, started_ts, "open", ts),
                )
            db.commit()

    def set_crm_id(self, incident_key: str, crm_ticket_id: int) -> None:
        with self._connect() as db:
            db.execute(
                "UPDATE tickets SET crm_ticket_id=?, last_update_ts=? WHERE incident_key=?",
                (int(crm_ticket_id), int(time.time()), incident_key),
            )
            db.commit()

    def mark_closed(self, incident_key: str, *, closed_ts: int) -> None:
        with self._connect() as db:
            db.execute(
                "UPDATE tickets SET status=?, closed_ts=?, last_update_ts=? WHERE incident_key=?",
                ("closed", int(closed_ts), int(time.time()), incident_key),
            )
            db.commit()


@dataclass
class TicketManager:
    """
    CRM wrapper + local store. Accepts either:
      TicketManager(store, uisp_client)   # uses client.crm_base / client.crm_token
      TicketManager(store, crm_base, crm_token)
    """
    store: TicketStore
    crm_base: str
    crm_token: str

    def __init__(self, store: TicketStore, client_or_base, token: str | None = None):
        self.store = store
        # Accept UispClient or (base, token)
        if hasattr(client_or_base, "crm_base") and hasattr(client_or_base, "crm_token"):
            self.crm_base = (client_or_base.crm_base or "").rstrip("/")
            self.crm_token = getattr(client_or_base, "crm_token", "")
        else:
            self.crm_base = (str(client_or_base) if client_or_base else "").rstrip("/")
            self.crm_token = token or ""

    # -------- internal HTTP --------
    def _crm(self, method: str, path: str, **kwargs):
        if not self.crm_base or not self.crm_token:
            raise RuntimeError("CRM base/token missing")
        url = f"{self.crm_base}/{path.lstrip('/')}"
        headers = kwargs.pop("headers", {})
        headers["x-auth-token"] = self.crm_token
        timeout = kwargs.pop("timeout", 15)
        resp = requests.request(method, url, headers=headers, timeout=timeout, **kwargs)
        if resp.status_code >= 400:
            raise RuntimeError(f"{resp.status_code}: {resp.text}")
        ct = resp.headers.get("Content-Type", "")
        if "application/json" in ct:
            return resp.json()
        return resp.text

    # -------- external API for tasks.py --------
    def open_minor_ticket(
        self,
        *,
        incident_key: str,
        client_id: str,
        endpoint_name: str,
        onu_id: str,
        started_ts: int,
        address: str,
        extra_note: str | None = None,
    ) -> int | None:
        """
        Idempotent open: upsert local row; if CRM configured, create ticket and store its id.
        Returns CRM ticket id if created; otherwise None.
        """
        self.store.upsert_open(
            incident_key,
            client_id=str(client_id),
            endpoint_name=endpoint_name,
            onu_id=onu_id,
            started_ts=int(started_ts),
            address=address or "-",
        )

        if not (self.crm_base and self.crm_token and client_id):
            log.info("ticket: local-only (CRM base/token or client_id missing) for %s", endpoint_name)
            return None

        payload = {
            "subject": f"[ESVC] Minor outage — {endpoint_name}",
            "clientId": int(client_id),
            "status": 0,  # open
            "public": False,
            "activity": [
                {
                    "userId": None,
                    "public": True,
                    "comment": {
                        "body": (
                            f"Outage detected for {endpoint_name} "
                            f"(ONU {onu_id}) at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(started_ts))}."
                            f"\nAddress: {address or '-'}"
                            + (f"\n{extra_note}" if extra_note else "")
                        )
                    },
                }
            ],
        }
        try:
            data = self._crm("POST", "/ticketing/tickets", json=payload)
            if isinstance(data, dict) and "id" in data:
                self.store.set_crm_id(incident_key, int(data["id"]))
                log.info("ticket: opened CRM #%s for %s", data["id"], endpoint_name)
                return int(data["id"])
            log.info("ticket: CRM create returned unexpected response: %r", data)
            return None
        except Exception as e:
            log.warning("ticket: CRM create failed for %s (%s): %s", endpoint_name, incident_key, e)
            return None

    def close_minor_ticket(self, *, incident_key: str, closed_ts: int, note: str | None = None) -> None:
        # Update local store first (idempotent)
        self.store.mark_closed(incident_key, closed_ts=closed_ts)

        row = self.store.get(incident_key)
        crm_id = row.get("crm_ticket_id") if row else None
        if not crm_id:
            log.info("ticket: local close (no CRM id) for incident %s", incident_key)
            return

        # Optional resolution note
        if note:
            try:
                self._crm(
                    "POST",
                    f"/ticketing/tickets/{crm_id}/activity",
                    json={"userId": None, "public": True, "comment": {"body": note}},
                )
            except Exception as e:
                log.warning("ticket: add resolution note failed for #%s: %s", crm_id, e)

        # Many instances expect status=4 for 'closed'. If your instance differs,
        # change 4 to the appropriate code (some use 3). We’ll also try fallback calls.
        payload = {"status": 4}

        # Try PATCH → PUT → POST /close
        try:
            self._crm("PATCH", f"/ticketing/tickets/{crm_id}", json=payload)
            log.info("ticket: closed CRM #%s (PATCH)", crm_id)
            return
        except Exception as e1:
            try:
                self._crm("PUT", f"/ticketing/tickets/{crm_id}", json=payload)
                log.info("ticket: closed CRM #%s (PUT fallback)", crm_id)
                return
            except Exception as e2:
                try:
                    # Last-resort close endpoint (not in all deployments)
                    self._crm("POST", f"/ticketing/tickets/{crm_id}/close", json={})
                    log.info("ticket: closed CRM #%s (POST /close fallback)", crm_id)
                    return
                except Exception as e3:
                    log.warning(
                        "ticket: CRM close failed for #%s (PATCH→PUT→POST/close): %s / %s / %s",
                        crm_id, e1, e2, e3
                    )

