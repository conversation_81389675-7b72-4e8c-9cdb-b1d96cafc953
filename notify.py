# notify.py
from __future__ import annotations

import logging
import time
import urllib.parse
from typing import Iterable
import smtplib
import ssl
from email.message import EmailMessage
import os
import requests

from config import settings

log = logging.getLogger("notify")


def normalize_phone(raw: str | None) -> str | None:
    """Normalize to E.164-ish. Return None if empty/unparseable."""
    if not raw:
        return None
    s = "".join(ch for ch in str(raw) if ch.isdigit() or ch == "+").strip()
    if not s:
        return None
    if s.startswith("+"):
        return s
    digits = "".join(ch for ch in s if ch.isdigit())
    if not digits:
        return None
    if len(digits) == 11 and digits.startswith("1"):
        return f"+{digits}"
    if len(digits) == 10:
        return f"+1{digits}"
    return digits


_last_sms_ts: float = 0.0  # simple, process-local pacing


def _parse_vitelity_ok(text: str) -> bool:
    """Vitelity success token inside HTML."""
    try:
        return "x[[ok[[x" in (text or "").lower()
    except Exception:
        return False


def send_sms_raw(phone: str, body: str, *, timeout: float = 10.0) -> bool:
    """
    Send SMS via Vitelity HTTP API (idempotent from caller’s perspective).
    Return True only if provider returns the 'ok' token. Quiet on HTML page noise.
    """
    norm = normalize_phone(phone)
    if not norm:
        log.warning("[SMS] invalid phone: %r", phone)
        return False

    params = {
        "login": settings.VITELITY_USERNAME,
        "pass": settings.VITELITY_PASSWORD,
        "cmd": "sendsms",
        "src": settings.VITELITY_FROM_NUMBER,
        "dst": norm.lstrip("+"),
        "msg": body,
    }
    url = "https://smsout-api.vitelity.net/api.php?" + urllib.parse.urlencode(params)
    log.info("[SMS] %s -> %s", norm, body)
    try:
        resp = requests.get(url, timeout=timeout)
        ok = _parse_vitelity_ok(resp.text)
        if ok:
            log.info("[SMS] accepted :: http=%s", resp.status_code)
        else:
            # provider returns a verbose HTML page; that’s not actionable—keep it at DEBUG
            snippet = (resp.text or "").strip()
            if len(snippet) > 240:
                snippet = snippet[:240] + "..."
            log.debug("[SMS] provider reply (not-ok): http=%s body=%r", resp.status_code, snippet)
        return ok
    except requests.RequestException as e:
        log.warning("[SMS] transport error: %s", e)
        return False


def send_sms_rate_limited(phone: str, body: str, *, min_gap_sec: float = 3.0) -> bool:
    """Enforce a small gap between provider calls."""
    global _last_sms_ts
    now = time.time()
    wait = _last_sms_ts + min_gap_sec - now
    if wait > 0:
        time.sleep(wait)
    ok = send_sms_raw(phone, body)
    _last_sms_ts = time.time()
    return ok

def send_email(recipients, subject: str, body: str) -> None:
    """
    Send a plain-text email via SMTP (Outlook 365 by default).
    Falls back to logging-only if SMTP isn't configured.
    Honors optional EMAIL_REDIRECT_TO to force all mail to a test address.
    """
    to_list = [str(r).strip() for r in (recipients or []) if r]
    if not to_list:
        log.info("[EMAIL] no recipients for subject=%r", subject)
        return

    # Pull SMTP config from config.py if present, else env vars
    try:
        from config import settings  # type: ignore
    except Exception:
        settings = None  # noqa: F841

    host = (getattr(settings, "SMTP_HOST", None) or
            os.getenv("SMTP_HOST") or "smtp.office365.com")
    port = int(getattr(settings, "SMTP_PORT", None) or
               os.getenv("SMTP_PORT") or "587")
    user = (getattr(settings, "SMTP_USER", None) or
            os.getenv("SMTP_USER") or "")
    password = (getattr(settings, "SMTP_PASSWORD", None) or
                os.getenv("SMTP_PASSWORD") or "")
    from_addr = (getattr(settings, "FROM_EMAIL", None) or
                 os.getenv("FROM_EMAIL") or user)

    # Optional: redirect all email to a safe test inbox (comma-separated)
    redirect = (getattr(settings, "EMAIL_REDIRECT_TO", None) or
                os.getenv("EMAIL_REDIRECT_TO") or "").strip()
    if redirect:
        to_list = [s.strip() for s in redirect.split(",") if s.strip()]

    # If we don't have creds, log and bail gracefully (keeps tests running)
    if not (host and user and password and from_addr):
        log.info("[EMAIL] (dry-run) to=%s subj=%s body=%s",
                 ", ".join(to_list), subject, body)
        return

    # Compose and send
    msg = EmailMessage()
    msg["Subject"] = subject
    msg["From"] = from_addr              # O365 usually requires this == user mailbox
    msg["To"] = ", ".join(to_list)
    msg.set_content(body)

    try:
        with smtplib.SMTP(host, port, timeout=20) as s:
            s.ehlo()
            s.starttls(context=ssl.create_default_context())
            s.ehlo()
            s.login(user, password)
            s.send_message(msg)
        log.info("[EMAIL] sent to=%s subj=%s", ", ".join(to_list), subject)
    except Exception as e:
        log.warning("[EMAIL] send failed: %s", e)
