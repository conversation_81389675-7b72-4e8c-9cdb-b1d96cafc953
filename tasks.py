# tasks.py
import time
import os
import logging
from typing import Dict, Set
from celery_app import app
from config import settings
from sms_store import SmsStore
from token_bucket import TokenBucket
from notify import send_sms_raw, send_email, normalize_phone
from detector import detect_offline_rows, _normalize_phone_e164
from ticketing import TicketStore,TicketManager
from uisp_client import UispClient

log = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(name)s: %(message)s")

#start ticketing system
uisp = UispClient(
    settings.NSM_BASE_URL,
    settings.NSM_TOKEN,
    crm_base=settings.CRM_BASE_URL,
    crm_token=settings.CRM_TOKEN,
    timeout=15.0,   # or add settings.HTTP_TIMEOUT
)
ticket_store = TicketStore(settings.TICKET_STORE_PATH)
tickets = TicketManager(ticket_store, uisp)

# in-memory per-worker state (stateless across restarts; OK for MVP)
_incident_started: Dict[str, float] = {}
_notified_initial: Set[str] = set()
_recent_off_ids: list[tuple[str, float]] = []  # (onu_id, ts)
_cluster_active: bool = False

store = SmsStore(settings.SMS_STORE_PATH)
rate = TokenBucket(settings.SMS_RATE_PER_MIN, settings.SMS_RATE_PER_MIN)

def _dev_off_key(onu_id: str) -> str: return f"dev:{onu_id}:offline"
def _dev_res_key(onu_id: str) -> str: return f"dev:{onu_id}:restore"
def _cluster_off_key() -> str: return "cluster:camden:offline"
def _cluster_res_key() -> str: return "cluster:camden:restore"

def _send_sms_once(phone: str, key: str, body: str, dedupe_secs: int = 7*24*3600):
    if not phone:
        return
    # 🔒 normalize right here so both dedupe and sending use the canonical value
    phone = normalize_phone(phone)
    if not phone:
        return
    if store.recently_sent(phone, key, dedupe_secs):
        return
    if rate.allow(1):
        send_sms_raw(phone, body)
        store.log_sms(phone, key, body)

def _normalize_phone_local(p: str | None) -> str | None:
    if not p:
        return None
    digits = "".join(ch for ch in p if ch.isdigit())
    if len(digits) == 10:
        return f"+1{digits}"
    if len(digits) == 11 and digits.startswith("1"):
        return f"+{digits}"
    return None  # be strict for SMS


def _compose_addr_from_client(client: dict) -> str:
    # Prefer fullAddress if present, else street1/city/zipCode (your CRM fields)
    full = (client.get("fullAddress") or "").strip()
    if full:
        return full
    street = (client.get("street1") or "").strip()
    city   = (client.get("city") or "").strip()
    zipc   = (client.get("zipCode") or "").strip()
    parts = [p for p in (street, city, zipc) if p]
    return ", ".join(parts) if parts else "-"


def choose_channel(row: dict) -> str | None:
    sms_ok   = bool(row.get("sms_enabled")) and bool(row.get("phone"))
    email_ok = bool(row.get("email_enabled")) and bool(row.get("email"))
    if sms_ok and email_ok:
        return "sms" if settings.NOTIFY_PREF == "sms_first" else "email"
    if sms_ok:
        return "sms"
    if email_ok:
        return "email"
    return None

def _resolve_client_id_by_endpoint(endpoint_name: str) -> str | None:
    """Best-effort CRM lookup to get client_id for a given endpoint display name."""
    if not endpoint_name:
        return None
    try:
        info = uisp.crm_lookup_contact_by_name(endpoint_name) or {}
        cid = info.get("client_id") or info.get("id") or info.get("clientId")
        return str(cid) if cid else None
    except Exception as e:
        log.warning("ticket: CRM lookup failed for %r: %s", endpoint_name, e)
        return None

def _format_address_from_crm_obj(c: dict) -> str:
    """
    Build 'street, city, zip' from a CRM client object.
    Falls back cleanly if any piece is missing.
    """
    if not isinstance(c, dict):
        return "-"
    parts = []
    for k in ("street1", "city", "zipCode"):
        v = (c.get(k) or "").strip()
        if v:
            parts.append(v)
    return ", ".join(parts) if parts else "-"

def _ensure_address(uisp, row: dict) -> str:
    """
    Prefer whatever detector provided; if '-', try CRM /clients/{id}.
    """
    addr = row.get("address") or row.get("addr") or "-"
    if addr != "-":
        return addr
    client_id = row.get("client_id")
    if not client_id:
        return "-"
    try:
        c = uisp.crm_get_client(int(client_id))
        if c:
            return _format_address_from_crm_obj(c)
    except Exception as e:
        logging.getLogger("tasks").debug("contact: address fallback failed for client %s: %s", client_id, e)
    return "-"

def _contact_for_row(row: dict) -> dict:
    """
    Build contact info for a row.
    Prefers data already present on the row, then CRM by client_id, then CRM name search.
    Flags default to YES unless explicitly falsey.
    Returns: {"email": str|None, "phone": str|None, "sms_enabled": bool|None, "email_enabled": bool|None}
    """
    def _yn(v) -> bool | None:
        if v is None:
            return None
        if isinstance(v, str):
            s = v.strip().lower()
            if s in {"yes", "y", "true", "t", "1"}:
                return True
            if s in {"no", "n", "false", "f", "0"}:
                return False
        return bool(v)

    email = row.get("email") or None
    phone = row.get("phone") or None
    sms_enabled = row.get("sms_enabled") if "sms_enabled" in row else None
    email_enabled = row.get("email_enabled") if "email_enabled" in row else None

    # Try CRM by client_id first (more deterministic than name search)
    cid = row.get("client_id")
    if cid and (email is None or phone is None or sms_enabled is None or email_enabled is None):
        try:
            c = uisp.crm_get_client(int(cid)) or {}
            # pick first contact that has an email or phone
            contacts = c.get("contacts") or []
            best = None
            for ct in contacts:
                if ct.get("email") or ct.get("phone"):
                    best = ct
                    break
            if best:
                email = email or (best.get("email") or None)
                phone = phone or (best.get("phone") or None)
            # flags may be custom attributes (array of {key,value})
            attrs = {a.get("key"): a.get("value") for a in (c.get("attributes") or []) if a.get("key")}
            if sms_enabled is None and "sms_notify" in attrs:
                sms_enabled = _yn(attrs.get("sms_notify"))
            if email_enabled is None and "email_notify" in attrs:
                email_enabled = _yn(attrs.get("email_notify"))
        except Exception as e:
            log.warning("contact: CRM id lookup failed for client %s: %s", cid, e)

    # Fallback: CRM name search
    if (email is None or phone is None or sms_enabled is None or email_enabled is None) and row.get("endpoint"):
        try:
            info = uisp.crm_lookup_contact_by_name(row["endpoint"]) or {}
            email = email or (info.get("email") or None)
            phone = phone or (info.get("phone") or None)
            if sms_enabled is None:
                sms_enabled = _yn(info.get("sms_notify"))
            if email_enabled is None:
                email_enabled = _yn(info.get("email_notify"))
        except Exception as e:
            log.warning("contact: CRM name lookup failed for %s: %s", row["endpoint"], e)

    return {
        "email": email,
        "phone": phone,
        "sms_enabled": sms_enabled,
        "email_enabled": email_enabled,
    }


@app.task(name="tasks.poll_and_notify")
def poll_and_notify():
    """
    Single poll pass:
      - detect offline ONUs across regions
      - maintain per-device timers
      - notify customers after PER_DEVICE_NOTIFY_AFTER_MIN (fallback to PER_DEVICE_THRESHOLD_MIN)
      - open a MINOR ticket for each new single-customer outage (no cluster)
      - on restore, SMS restore and close ticket
      - compute cluster severity and escalate
    """
    import time as _time
    from datetime import datetime
    from zoneinfo import ZoneInfo

    global _incident_started, _notified_initial, _recent_off_ids

    now_ts = _time.time()
    store.prune_older_than_days(7)
    # --- testing override ---
    TEST_MODE = (os.getenv("TEST_MODE", "").strip() in {"1", "true", "yes"})
    # Support SMS list: prefer SUPPORT_SMS if set, else fall back to NOC_HD365_SMS
    _support_sms = []
    try:
        # SUPPORT_SMS can be comma-separated
        env_support_sms = os.getenv("SUPPORT_SMS", "")
        if env_support_sms.strip():
            _support_sms = [s.strip() for s in env_support_sms.split(",") if s.strip()]
        elif hasattr(settings, "NOC_HD365_SMS"):
            _support_sms = list(getattr(settings, "NOC_HD365_SMS") or [])
    except Exception:
        _support_sms = []

    # Regions may be a comma-string or a list
    if isinstance(settings.REGION_KEYS, str):
        region_list = [r.strip() for r in settings.REGION_KEYS.split(",") if r.strip()]
    else:
        region_list = list(settings.REGION_KEYS)

    # ---------- 1) Detect current offlines ----------
    active_off = {}  # onu_id -> row

    def _ensure_address(row: dict) -> str:
        # Prefer any address already present on row
        addr = (row.get("address") or row.get("client_addr") or "").strip()
        if addr:
            return addr

        # Try by client_id
        cid = row.get("client_id")
        if cid:
            try:
                c = uisp.crm_get_client(int(cid)) or {}
                s1 = (c.get("street1") or "").strip()
                cty = (c.get("city") or "").strip()
                zp = (c.get("zipCode") or "").strip()
                if s1 or cty or zp:
                    return ", ".join([x for x in (s1, cty, zp) if x])
            except Exception as e:
                log.warning("address: CRM get_client failed for id=%s: %s", cid, e)

        # Fallback: CRM lookup by name (synthetic rows)
        name = (row.get("endpoint") or "").strip()
        if name:
            try:
                info = uisp.crm_lookup_contact_by_name(name) or {}
                c_id = info.get("client_id")
                if c_id:
                    c = uisp.crm_get_client(int(c_id)) or {}
                    s1 = (c.get("street1") or "").strip()
                    cty = (c.get("city") or "").strip()
                    zp = (c.get("zipCode") or "").strip()
                    if s1 or cty or zp:
                        return ", ".join([x for x in (s1, cty, zp) if x])
            except Exception as e:
                log.warning("address: CRM name lookup failed for %s: %s", name, e)

        return "-"

    rows_this_cycle = []
    for region in region_list:
        try:
            for row in detect_offline_rows(region):
                row["client_addr"] = _ensure_address(row)
                rows_this_cycle.append(row)
                active_off[row["onu_id"]] = row
        except Exception as e:
            log.error("detect_offline_rows failed for region=%s: %s", region, e)

    detail_str = ", ".join(f"{r.get('endpoint')}({r.get('onu_id')})" for r in rows_this_cycle) or "<none>"
    log.info("offline detail: %s", detail_str)

    # ---------- 2) Per-device timers + customer notify + ticket open ----------
    new_this_poll = []  # first seen this poll
    for onu_id, row in active_off.items():
        first_seen = _incident_started.get(onu_id)
        if first_seen is None:
            _incident_started[onu_id] = now_ts
            new_this_poll.append(row)
        _recent_off_ids.append((onu_id, now_ts))
        # Build contact (uses your existing helper)
        contact = _contact_for_row(row)
        phone = _normalize_phone_e164(contact.get("phone"))
        email = contact.get("email")

        # Assume YES unless explicitly False
        sms_enabled = contact.get("sms_enabled")
        if sms_enabled is None:
            sms_enabled = True if phone else False
        email_enabled = contact.get("email_enabled")
        if email_enabled is None:
            email_enabled = True if email else False

        row.update(
            {
                "phone": phone,
                "email": email,
                "sms_enabled": sms_enabled,
                "email_enabled": email_enabled,
            }
        )

        # notify after threshold, once per incident
        elapsed_min = (now_ts - _incident_started[onu_id]) / 60.0
        if elapsed_min >= settings.PER_DEVICE_THRESHOLD_MIN and onu_id not in _notified_initial:
            endpoint_name = row.get("endpoint") or ""
            addr = (row.get("client_addr") or row.get("address") or "-")
            started_dt = datetime.fromtimestamp(_incident_started[onu_id])
            started_str = started_dt.strftime("%Y-%m-%d %H:%M:%S")

            if TEST_MODE:
                # During tests, DO NOT contact customer; page support instead
                if _support_sms:
                    body = f"[ESVC] Minor outage: {endpoint_name} at {addr} since {started_str}."
                    for n in _support_sms:
                        _send_sms_once(n, _dev_off_key(onu_id), body)
                else:
                    log.info("notify: TEST_MODE on but SUPPORT list is empty; suppressing.")
            else:
                # Production behavior: choose customer's preferred channel
                chan = choose_channel(row)  # "sms" | "email" | "none"
                if chan == "sms" and row.get("phone"):
                    _send_sms_once(row["phone"], _dev_off_key(onu_id),
                                   f"[ESVC] Service interruption detected for {endpoint_name}.")
                    log.info("notify: using SMS for %s", endpoint_name)
                elif chan == "email" and row.get("email"):
                    send_email([row["email"]], "Service interruption detected",
                               f"Hi, we detected a temporary service interruption for {endpoint_name}.")
                    log.info("notify: using EMAIL for %s", endpoint_name)
                else:
                    log.info("notify: suppressed (no allowed channel) for %s", endpoint_name)

            _notified_initial.add(onu_id)

    # ---------- 3) Restoration flow ----------
    current_ids = set(active_off.keys())
    to_restore = [dev_id for dev_id in list(_incident_started.keys()) if dev_id not in current_ids]
    for onu_id in to_restore:
        _incident_started.pop(onu_id, None)
        _notified_initial.discard(onu_id)
        for recipient in store.recipients_for_key(_dev_off_key(onu_id), within_days=7):
            _send_sms_once(recipient, _dev_res_key(onu_id), "[ESVC] Service has been restored. Thank you for your patience.")
            _time.sleep(3.0)
        try:
            tickets.close_minor_ticket(incident_key=f"dev:{onu_id}", closed_ts=int(now_ts), note="Service restored.")
        except Exception as e:
            log.warning("ticket: close failed for %s: %s", onu_id, e)

    # ---------- 4) Cluster severity / escalations ----------
    window_secs = getattr(settings, "CLUSTER_WINDOW_SECS", 120)
    window_start = now_ts - window_secs
    while _recent_off_ids and _recent_off_ids[0][1] < window_start:
        _recent_off_ids.pop(0)
    unique_in_window = {dev for (dev, ts) in _recent_off_ids}
    count_in_window = len(unique_in_window)

    MODERATE_N = getattr(settings, "CLUSTER_THRESHOLD", 5)
    URGENT_N = getattr(settings, "CLUSTER_URGENT_N", getattr(settings, "CLUSTER_URGENT_M", max(MODERATE_N + 1, 10)))
    URGENT_WINDOW_SECS = getattr(settings, "URGENT_WINDOW_SECS", 120)
    CRITICAL_MINUTES = getattr(settings, "SITE_CRITICAL_MINUTES", 20)

    urgent_cut = now_ts - URGENT_WINDOW_SECS
    urgent_count = len({dev for (dev, ts) in _recent_off_ids if ts >= urgent_cut})

    if count_in_window == 0:
        severity = "none"
    elif count_in_window < MODERATE_N:
        severity = "minor"
    elif urgent_count >= URGENT_N:
        severity = "urgent"
    else:
        severity = "moderate"

    # critical if the cluster persists
    try:
        first_cluster_ts = poll_and_notify._first_cluster_ts
    except AttributeError:
        first_cluster_ts = None
    if severity in {"moderate", "urgent"}:
        if first_cluster_ts is None:
            poll_and_notify._first_cluster_ts = now_ts
        else:
            if (now_ts - first_cluster_ts) / 60.0 >= CRITICAL_MINUTES:
                severity = "critical"
    else:
        poll_and_notify._first_cluster_ts = None

    # ---------- 5) Open MINOR ticket(s) for single-customer events only ----------
    if severity == "minor":
        if new_this_poll:
            for r in new_this_poll:
                onu_id = r.get("onu_id")
                endpoint = r.get("endpoint") or ""
                client_id = r.get("client_id") or 13  # default test client
                address = r.get("client_addr") or "-"
                try:
                    tickets.open_minor_ticket(
                        incident_key=f"dev:{onu_id}",
                        client_id=str(client_id),
                        endpoint_name=endpoint,
                        onu_id=onu_id,
                        started_ts=int(now_ts),
                        address=address,
                        extra_note=None,
                    )
                except Exception as e:
                    log.warning("ticket: open failed for %s (%s): %s", endpoint, onu_id, e)
        else:
            log.info("ticket: skipped (minor but 0 outages this cycle)")

    # ---------- 6) Staff escalations ----------
    lines = [
        "* {ep} — Client #{cid} — {addr}".format(
            ep=r.get("endpoint"),
            cid=r.get("client_id") or "-",
            addr=r.get("client_addr") or "-",
        )
        for r in rows_this_cycle
    ] or ["No current customer outages."]

    cluster_state = "off" if severity in {"none","minor"} else "ON"
    log.info("poll: offline=%d cluster=%s (severity=%s)", len(active_off), cluster_state, severity)

    def _is_after_hours(ts: float) -> bool:
        try:
            tz = ZoneInfo(getattr(settings, "TIMEZONE", "America/New_York"))
        except Exception:
            tz = None
        lt = datetime.fromtimestamp(ts, tz) if tz else datetime.fromtimestamp(ts)
        start_s = getattr(settings, "OFFICE_HOURS", {}).get("start", "08:00")
        end_s = getattr(settings, "OFFICE_HOURS", {}).get("end", "18:00")
        sh, sm = [int(x) for x in start_s.split(":")]
        eh, em = [int(x) for x in end_s.split(":")]
        start_mins = sh * 60 + sm
        end_mins = eh * 60 + em
        cur_mins = lt.hour * 60 + lt.minute
        return not (start_mins <= cur_mins < end_mins)

    after_hours = _is_after_hours(now_ts)

    if severity == "moderate":
        if after_hours:
            for n in getattr(settings, "NOC_HD365_SMS", []) or []:
                _send_sms_once(n, f"cluster:{int(window_start)}", "[NOC] Moderate outage detected (after hours).")
                _time.sleep(3.0)
        if getattr(settings, "MODERATE_MAIL", ""):
            send_email([settings.MODERATE_MAIL], "[ESVC] Moderate outage", "\n".join(lines))

    elif severity == "urgent":
        for n in set((getattr(settings, "NOC_HD365_SMS", []) or []) + (getattr(settings, "FIELD_SUPERVISOR_SMS", []) or [])):
            _send_sms_once(n, f"cluster:{int(window_start)}", "[NOC/FIELD] URGENT outage condition.")
            _time.sleep(3.0)
        if getattr(settings, "URGENT_MAIL", ""):
            send_email([settings.URGENT_MAIL], "[ESVC] URGENT outage", "\n".join(lines))

    elif severity == "critical":
        for n in set((getattr(settings, "NOC_HD365_SMS", []) or []) + (getattr(settings, "FIELD_SUPERVISOR_SMS", []) or [])):
            _send_sms_once(n, f"cluster:{int(window_start)}", "[NOC/FIELD] CRITICAL outage condition.")
            _time.sleep(3.0)
        if getattr(settings, "CRITICAL_MAIL", ""):
            send_email([settings.CRITICAL_MAIL], "[ESVC] CRITICAL outage", "\n".join(lines))

